export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  createdAt: number
  updatedAt: number
}

export interface BankAccount {
  id: string
  userId: string
  accountId: string // Stitch account ID
  accountName: string
  accountType: "checking" | "savings" | "credit"
  bankName: string
  balance: number
  currency: string
  isActive: boolean
  lastSynced: number
  createdAt: number
}

export interface Transaction {
  id: string
  userId: string
  accountId: string
  transactionId: string // Stitch transaction ID
  amount: number
  currency: string
  description: string
  category: TransactionCategory
  subcategory?: string
  date: number
  type: "debit" | "credit"
  merchant?: string
  location?: string
  isRecurring: boolean
  tags: string[]
  createdAt: number
}

export type TransactionCategory = 
  | "groceries"
  | "transport"
  | "entertainment"
  | "utilities"
  | "healthcare"
  | "education"
  | "shopping"
  | "dining"
  | "travel"
  | "insurance"
  | "investments"
  | "income"
  | "transfers"
  | "fees"
  | "other"

export interface Budget {
  id: string
  userId: string
  name: string
  category: TransactionCategory
  amount: number
  spent: number
  period: "weekly" | "monthly" | "yearly"
  startDate: number
  endDate: number
  isActive: boolean
  alerts: {
    enabled: boolean
    threshold: number // percentage
  }
  createdAt: number
  updatedAt: number
}

export interface SavingsGoal {
  id: string
  userId: string
  name: string
  description?: string
  targetAmount: number
  currentAmount: number
  targetDate: number
  category: "emergency" | "vacation" | "purchase" | "investment" | "other"
  isActive: boolean
  autoSave: {
    enabled: boolean
    amount: number
    frequency: "daily" | "weekly" | "monthly"
  }
  createdAt: number
  updatedAt: number
}

export interface FinancialInsight {
  id: string
  userId: string
  type: "spending_pattern" | "budget_recommendation" | "savings_opportunity" | "bill_prediction"
  title: string
  description: string
  impact: "low" | "medium" | "high"
  category?: TransactionCategory
  actionable: boolean
  action?: {
    type: "create_budget" | "adjust_budget" | "set_savings_goal" | "review_subscription"
    data: any
  }
  isRead: boolean
  createdAt: number
}

export interface StokvelGroup {
  id: string
  name: string
  description?: string
  adminId: string
  members: StokvelMember[]
  contributionAmount: number
  contributionFrequency: "weekly" | "monthly"
  payoutSchedule: "rotating" | "fixed_date"
  nextPayoutDate: number
  totalContributions: number
  isActive: boolean
  createdAt: number
}

export interface StokvelMember {
  userId: string
  joinedAt: number
  totalContributed: number
  lastContribution: number
  payoutPosition: number
  isActive: boolean
}

export interface Bill {
  id: string
  userId: string
  name: string
  category: TransactionCategory
  amount: number
  dueDate: number
  frequency: "monthly" | "quarterly" | "annually"
  isAutoPay: boolean
  accountId?: string
  predictedAmount?: number
  isActive: boolean
  createdAt: number
  updatedAt: number
}

export interface AIRecommendation {
  id: string
  userId: string
  type: "budget" | "savings" | "investment" | "debt_management"
  title: string
  description: string
  confidence: number // 0-1
  potentialSavings?: number
  timeframe: string
  steps: string[]
  isImplemented: boolean
  createdAt: number
}
