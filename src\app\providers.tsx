'use client';

import { ReactNode } from 'react';
import { ConvexProviderWithClerk } from 'convex/react-clerk';
import { Clerk<PERSON>rovider, useAuth } from '@clerk/nextjs';
import { ConvexReactClient } from 'convex/react';

// Initialize Convex client
const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export function ConvexClientProvider({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <ClerkProvider
      publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
    >
      <ConvexProviderWithClerk 
        client={convex} 
        useAuth={useAuth}
      >
        {children}
      </ConvexProviderWithClerk>
    </ClerkProvider>
  );
}
