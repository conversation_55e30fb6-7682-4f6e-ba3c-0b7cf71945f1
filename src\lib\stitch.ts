// Stitch API integration utilities
export interface StitchConfig {
  clientId: string;
  clientSecret: string;
  apiUrl: string;
  redirectUrl: string;
}

export interface StitchAccount {
  id: string;
  name: string;
  accountType: "checking" | "savings" | "credit";
  bankName: string;
  balance: number;
  currency: string;
}

export interface StitchTransaction {
  id: string;
  amount: number;
  currency: string;
  description: string;
  date: string;
  type: "debit" | "credit";
  merchant?: string;
  category?: string;
}

export interface StitchAuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

export class StitchAPI {
  private config: StitchConfig;
  private accessToken?: string;
  private tokenExpiry?: number;

  constructor(config: StitchConfig) {
    this.config = config;
  }

  private async getAccessToken(): Promise<string> {
    if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
      return this.accessToken;
    }

    const response = await fetch(`${this.config.apiUrl}/connect/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        scope: 'accounts transactions payments',
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to get access token: ${response.statusText}`);
    }

    const data: StitchAuthResponse = await response.json();
    this.accessToken = data.access_token;
    this.tokenExpiry = Date.now() + (data.expires_in * 1000) - 60000; // 1 minute buffer

    return this.accessToken;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const token = await this.getAccessToken();
    
    const response = await fetch(`${this.config.apiUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`Stitch API error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async getAuthorizationUrl(userId: string, scopes: string[] = ['accounts', 'transactions']): Promise<string> {
    const params = new URLSearchParams({
      client_id: this.config.clientId,
      response_type: 'code',
      redirect_uri: this.config.redirectUrl,
      scope: scopes.join(' '),
      state: userId, // Use userId as state for security
    });

    return `${this.config.apiUrl}/connect/authorize?${params.toString()}`;
  }

  async exchangeCodeForToken(code: string): Promise<StitchAuthResponse> {
    const response = await fetch(`${this.config.apiUrl}/connect/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        code: code,
        redirect_uri: this.config.redirectUrl,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to exchange code for token: ${response.statusText}`);
    }

    return response.json();
  }

  async getAccounts(userToken: string): Promise<StitchAccount[]> {
    const response = await fetch(`${this.config.apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query GetAccounts {
            user {
              accounts {
                id
                name
                accountType
                bankName
                balance {
                  amount
                  currency
                }
              }
            }
          }
        `,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch accounts: ${response.statusText}`);
    }

    const data = await response.json();
    
    return data.data.user.accounts.map((account: any) => ({
      id: account.id,
      name: account.name,
      accountType: account.accountType.toLowerCase(),
      bankName: account.bankName,
      balance: account.balance.amount,
      currency: account.balance.currency,
    }));
  }

  async getTransactions(userToken: string, accountId: string, limit: number = 100): Promise<StitchTransaction[]> {
    const response = await fetch(`${this.config.apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query GetTransactions($accountId: ID!, $limit: Int!) {
            user {
              account(id: $accountId) {
                transactions(first: $limit) {
                  edges {
                    node {
                      id
                      amount {
                        amount
                        currency
                      }
                      description
                      date
                      type
                      merchant
                      category
                    }
                  }
                }
              }
            }
          }
        `,
        variables: {
          accountId,
          limit,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch transactions: ${response.statusText}`);
    }

    const data = await response.json();
    
    return data.data.user.account.transactions.edges.map((edge: any) => ({
      id: edge.node.id,
      amount: edge.node.amount.amount,
      currency: edge.node.amount.currency,
      description: edge.node.description,
      date: edge.node.date,
      type: edge.node.type.toLowerCase(),
      merchant: edge.node.merchant,
      category: edge.node.category,
    }));
  }

  async initiatePayment(userToken: string, paymentData: {
    amount: number;
    currency: string;
    recipientAccountId: string;
    reference: string;
  }): Promise<{ paymentId: string; status: string }> {
    const response = await fetch(`${this.config.apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          mutation InitiatePayment($input: PaymentInput!) {
            initiatePayment(input: $input) {
              id
              status
            }
          }
        `,
        variables: {
          input: paymentData,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to initiate payment: ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      paymentId: data.data.initiatePayment.id,
      status: data.data.initiatePayment.status,
    };
  }
}

// Create a singleton instance
export const stitchAPI = new StitchAPI({
  clientId: process.env.STITCH_CLIENT_ID || '',
  clientSecret: process.env.STITCH_CLIENT_SECRET || '',
  apiUrl: process.env.STITCH_API_URL || 'https://api.stitch.money',
  redirectUrl: process.env.NEXT_PUBLIC_STITCH_REDIRECT_URL || 'http://localhost:3000/api/stitch/callback',
});

// Helper function to categorize transactions using AI/rules
export function categorizeTransaction(description: string, merchant?: string): string {
  const desc = description.toLowerCase();
  const merch = merchant?.toLowerCase() || '';
  
  // Grocery stores
  if (desc.includes('shoprite') || desc.includes('pick n pay') || desc.includes('checkers') || 
      desc.includes('woolworths') || desc.includes('spar') || merch.includes('grocery')) {
    return 'groceries';
  }
  
  // Transport
  if (desc.includes('uber') || desc.includes('bolt') || desc.includes('taxi') || 
      desc.includes('petrol') || desc.includes('fuel') || desc.includes('gautrain')) {
    return 'transport';
  }
  
  // Utilities
  if (desc.includes('eskom') || desc.includes('city power') || desc.includes('water') || 
      desc.includes('electricity') || desc.includes('municipal')) {
    return 'utilities';
  }
  
  // Entertainment
  if (desc.includes('netflix') || desc.includes('dstv') || desc.includes('cinema') || 
      desc.includes('movie') || desc.includes('showmax')) {
    return 'entertainment';
  }
  
  // Dining
  if (desc.includes('restaurant') || desc.includes('mcdonald') || desc.includes('kfc') || 
      desc.includes('steers') || desc.includes('nando')) {
    return 'dining';
  }
  
  // Healthcare
  if (desc.includes('pharmacy') || desc.includes('clicks') || desc.includes('dischem') || 
      desc.includes('doctor') || desc.includes('medical')) {
    return 'healthcare';
  }
  
  // Default category
  return 'other';
}
