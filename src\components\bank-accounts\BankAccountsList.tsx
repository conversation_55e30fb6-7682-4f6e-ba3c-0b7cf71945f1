'use client';

import { useState, useCallback } from 'react';
import { useUser } from '@clerk/nextjs';
import { useBankAccounts, type BankAccount } from '@/hooks/useBankAccounts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '@/lib/utils';
import { Plus, RefreshCw, Trash2, AlertCircle } from 'lucide-react';
import type { Id } from '@/convex/_generated/dataModel';

const CURRENCY_SYMBOLS: Record<string, string> = {
  ZAR: 'R',
  USD: '$',
  EUR: '€',
  GBP: '£',
};

interface BankAccountsListProps {
  onRefresh?: () => void;
  isRefreshing?: boolean;
}

export function BankAccountsList({ onRefresh, isRefreshing: externalIsRefreshing }: BankAccountsListProps) {
  const { user } = useUser();
  const { 
    accounts = [], 
    totalBalance = 0, 
    isLoading, 
    updateBalance, 
    deactivateAccount,
    error: bankAccountsError
  } = useBankAccounts(user?.id as Id<"users">);
  
  const [error, setError] = useState<Error | null>(bankAccountsError || null);
  
  // Update error state when bankAccountsError changes
  React.useEffect(() => {
    if (bankAccountsError && error !== bankAccountsError) {
      setError(bankAccountsError);
    }
  }, [bankAccountsError, error]);
  type AccountId = Id<"bankAccounts">;
  const [isDeactivating, setIsDeactivating] = useState<Record<string, boolean>>({});
  const [localRefreshing, setLocalRefreshing] = useState<Record<string, boolean>>({});
  
  // Helper to check if any account is refreshing
  const isAnyRefreshing = externalIsRefreshing || Object.values(localRefreshing).some(Boolean);
  
  // Helper function to safely access boolean values from the records
  const getIsRefreshing = useCallback((accountId: AccountId): boolean => {
    return Boolean(localRefreshing[accountId as string]);
  }, [localRefreshing]);
  
  const getIsDeactivating = useCallback((accountId: AccountId): boolean => {
    return Boolean(isDeactivating[accountId as string]);
  }, [isDeactivating]);
  
  // Helper to check if a specific account is refreshing
  const isAccountRefreshing = useCallback((accountId: AccountId): boolean => {
    return Boolean(externalIsRefreshing || getIsRefreshing(accountId));
  }, [externalIsRefreshing, getIsRefreshing]);
  
  // Helper functions to update loading states with proper type safety
  const setAccountRefreshing = useCallback((accountId: AccountId, loading: boolean) => {
    setLocalRefreshing(prev => {
      const newState = { ...prev };
      newState[accountId as string] = loading;
      return newState;
    });
  }, []);
  
  const setAccountDeactivating = useCallback((accountId: AccountId, loading: boolean) => {
    setIsDeactivating(prev => {
      const newState = { ...prev };
      newState[accountId as string] = loading;
      return newState;
    });
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-48" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-4" />
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <AlertCircle className="h-5 w-5 text-red-400" aria-hidden="true" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading accounts</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error.message || 'Failed to load bank accounts. Please try again later.'}</p>
            </div>
          </div>
        </div>
      </div>
        );
  }
  
  if (!accounts || accounts.length === 0) {
    return (
      <div className="text-center py-12 border-2 border-dashed rounded-lg">
        <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No accounts</h3>
        <p className="mt-1 text-sm text-gray-500">Get started by adding a bank account.</p>
        <div className="mt-6">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Account
          </Button>
        </div>
      </div>
    );
  }

  const handleRefreshBalance = useCallback(async (accountId: Id<"bankAccounts">) => {
    try {
      setAccountRefreshing(accountId, true);
      
      // In a real app, you would fetch the latest balance from your bank API
      // For now, we'll just simulate a small random change
      const account = accounts.find((acc) => acc._id === accountId);
      
      if (account) {
        const randomChange = Math.random() > 0.5 ? 100 : -50;
        const newBalance = Math.max(0, (account.balance || 0) + randomChange);
        
        await updateBalance(accountId, newBalance);
        
        // Call the parent refresh handler if provided
        if (onRefresh) {
          onRefresh();
        }
      }
    } catch (err) {
      console.error('Error refreshing balance:', err);
      setError(err instanceof Error ? err : new Error('Failed to refresh balance'));
    } finally {
      setAccountRefreshing(accountId, false);
    }
  }, [accounts, onRefresh, setAccountRefreshing, updateBalance]);

  const handleDeactivateAccount = useCallback(async (accountId: Id<"bankAccounts">) => {
    if (!window.confirm('Are you sure you want to deactivate this account? This action cannot be undone.')) {
      return;
    }
    
    try {
      setAccountDeactivating(accountId, true);
      await deactivateAccount(accountId);
      
      // Call the parent refresh handler if provided
      if (onRefresh) {
        onRefresh();
      }
      
      // You might want to show a success toast here
    } catch (err) {
      console.error('Error deactivating account:', err);
      setError(err instanceof Error ? err : new Error('Failed to deactivate account'));
    } finally {
      setAccountDeactivating(accountId, false);
    }
  }, [deactivateAccount, onRefresh, setAccountDeactivating]);

  // Memoize the account cards to prevent unnecessary re-renders
  const accountCards = useMemo(() => {
    if (!accounts || accounts.length === 0) {
      return (
        <div className="text-center py-12 border-2 border-dashed rounded-lg">
          <p className="text-muted-foreground">No bank accounts found</p>
          <Button className="mt-4" variant="outline">
            Connect Your Bank
          </Button>
        </div>
      );
    }

    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {accounts.map((account) => (
            <Card key={account._id} className="relative overflow-hidden">
              <div 
                className={`absolute top-0 left-0 w-1 h-full ${
                  account.accountType === 'checking' ? 'bg-blue-500' :
                  account.accountType === 'savings' ? 'bg-green-500' : 'bg-purple-500'
                }`} 
              />
              <CardHeader className="pb-2 pl-8">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{account.accountName}</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {account.bankName} • {account.accountType}
                    </p>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-8 w-8"
                    onClick={() => handleDeactivateAccount(account._id)}
                    disabled={getIsDeactivating(account._id)}
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="pl-8">
                <div className="flex items-end justify-between">
                  <div>
                    <p className="text-2xl font-bold">
                      {formatCurrency(account.balance, account.currency)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Last updated: {new Date(account.lastSynced).toLocaleString()}
                    </p>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleRefreshBalance(account._id)}
                    disabled={isAccountRefreshing(account._id)}
                  >
                    <RefreshCw className={`mr-2 h-3 w-3 ${getIsRefreshing(account._id) ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                </div>
              </CardContent>
            </Card>
        ))}
      </div>
    );
  }, [accounts, handleDeactivateAccount, handleRefreshBalance, isAccountRefreshing, getIsDeactivating]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-48" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-4" />
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <AlertCircle className="h-5 w-5 text-red-400" aria-hidden="true" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading accounts</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error.message || 'Failed to load bank accounts. Please try again later.'}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Bank Accounts</h2>
          <p className="text-muted-foreground">
            Total Balance: {formatCurrency(totalBalance, 'ZAR')}
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Account
        </Button>
      </div>
      {accountCards}
    </div>
  );
}
