"use client";

import { useState } from 'react';
import { formatCurrency } from '@/lib/utils';
import { MobileCard, TouchButton } from '@/components/layout/mobile-nav';
import { MobileTransactionCard } from './mobile-transaction-card';
import { useTransactions, useBudgets, useBankAccounts } from '@/hooks/useRealtime';
import {
  Eye,
  EyeOff,
  TrendingUp,
  TrendingDown,
  Plus,
  ArrowUpRight,
  ArrowDownRight,
  CreditCard,
  PiggyBank,
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Brain,
  BarChart3,
  Bell,
  Scan,
  Send,
  Wallet,
  RefreshCw,
} from 'lucide-react';

interface MobileDashboardProps {
  className?: string;
}

export function MobileDashboard({ className }: MobileDashboardProps) {
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  const { transactionsWithBalance } = useTransactions();
  const { budgetProgress } = useBudgets();
  const { accounts, totalBalance } = useBankAccounts();

  const recentTransactions = transactionsWithBalance?.transactions?.slice(0, 5) || [];
  const currentBalance = totalBalance || 0;
  
  // Calculate spending this month
  const thisMonth = new Date();
  const startOfMonth = new Date(thisMonth.getFullYear(), thisMonth.getMonth(), 1);
  const monthlySpending = recentTransactions
    .filter(t => t.type === 'debit' && new Date(t.date) >= startOfMonth)
    .reduce((sum, t) => sum + Math.abs(t.amount), 0);

  const monthlyIncome = recentTransactions
    .filter(t => t.type === 'credit' && new Date(t.date) >= startOfMonth)
    .reduce((sum, t) => sum + t.amount, 0);

  // Budget alerts
  const budgetAlerts = budgetProgress.filter(b => b.isOverBudget || b.isNearLimit);
  
  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => setRefreshing(false), 1000);
  };

  const quickActions = [
    { 
      icon: Plus, 
      label: 'Add Transaction', 
      color: 'bg-blue-600 hover:bg-blue-700',
      action: () => console.log('Add transaction')
    },
    { 
      icon: Scan, 
      label: 'Scan Receipt', 
      color: 'bg-green-600 hover:bg-green-700',
      action: () => console.log('Scan receipt')
    },
    { 
      icon: Send, 
      label: 'Send Money', 
      color: 'bg-purple-600 hover:bg-purple-700',
      action: () => console.log('Send money')
    },
    { 
      icon: Wallet, 
      label: 'Pay Bill', 
      color: 'bg-orange-600 hover:bg-orange-700',
      action: () => console.log('Pay bill')
    },
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Balance */}
      <MobileCard className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-lg font-semibold">Good morning! 👋</h1>
            <p className="text-blue-100 text-sm">Here's your financial overview</p>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="p-2 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 transition-colors"
          >
            <RefreshCw className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>

        <div className="space-y-4">
          {/* Total Balance */}
          <div>
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-blue-100 text-sm">Total Balance</span>
              <button
                onClick={() => setBalanceVisible(!balanceVisible)}
                className="p-1 rounded-full hover:bg-white hover:bg-opacity-20"
              >
                {balanceVisible ? (
                  <EyeOff className="w-4 h-4" />
                ) : (
                  <Eye className="w-4 h-4" />
                )}
              </button>
            </div>
            <div className="text-3xl font-bold">
              {balanceVisible ? formatCurrency(currentBalance) : '••••••'}
            </div>
          </div>

          {/* Monthly Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white bg-opacity-10 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-1">
                <ArrowDownRight className="w-4 h-4 text-red-300" />
                <span className="text-xs text-blue-100">This Month</span>
              </div>
              <div className="text-lg font-semibold">
                {balanceVisible ? formatCurrency(monthlySpending) : '••••'}
              </div>
              <div className="text-xs text-blue-100">Spent</div>
            </div>
            
            <div className="bg-white bg-opacity-10 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-1">
                <ArrowUpRight className="w-4 h-4 text-green-300" />
                <span className="text-xs text-blue-100">This Month</span>
              </div>
              <div className="text-lg font-semibold">
                {balanceVisible ? formatCurrency(monthlyIncome) : '••••'}
              </div>
              <div className="text-xs text-blue-100">Earned</div>
            </div>
          </div>
        </div>
      </MobileCard>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            return (
              <TouchButton
                key={index}
                onClick={action.action}
                className={`${action.color} h-16 flex-col space-y-1`}
              >
                <Icon className="w-6 h-6" />
                <span className="text-sm font-medium">{action.label}</span>
              </TouchButton>
            );
          })}
        </div>
      </div>

      {/* Budget Alerts */}
      {budgetAlerts.length > 0 && (
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-3">Budget Alerts</h2>
          <div className="space-y-3">
            {budgetAlerts.slice(0, 2).map((budget) => (
              <MobileCard key={budget._id} className="border-l-4 border-l-orange-500">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                    <AlertTriangle className="w-5 h-5 text-orange-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{budget.name}</h3>
                    <p className="text-sm text-gray-600">
                      {formatCurrency(budget.actualSpent)} of {formatCurrency(budget.amount)} used
                    </p>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                      <div 
                        className="bg-orange-500 h-2 rounded-full" 
                        style={{ width: `${Math.min(budget.percentage, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </MobileCard>
            ))}
          </div>
        </div>
      )}

      {/* Account Overview */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">Accounts</h2>
          <TouchButton 
            size="sm" 
            className="bg-gray-100 text-gray-700 hover:bg-gray-200"
            onClick={() => window.location.href = '/dashboard/bank-accounts'}
          >
            View All
          </TouchButton>
        </div>
        <div className="space-y-3">
          {accounts.slice(0, 2).map((account) => (
            <MobileCard key={account._id}>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <CreditCard className="w-5 h-5 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{account.name}</h3>
                  <p className="text-sm text-gray-600">{account.type}</p>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-gray-900">
                    {balanceVisible ? formatCurrency(account.balance) : '••••••'}
                  </div>
                  <div className="text-xs text-gray-500">{account.currency}</div>
                </div>
              </div>
            </MobileCard>
          ))}
        </div>
      </div>

      {/* Recent Transactions */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">Recent Transactions</h2>
          <TouchButton 
            size="sm" 
            className="bg-gray-100 text-gray-700 hover:bg-gray-200"
            onClick={() => window.location.href = '/dashboard/transactions'}
          >
            View All
          </TouchButton>
        </div>
        <div className="space-y-3">
          {recentTransactions.length > 0 ? (
            recentTransactions.map((transaction) => (
              <MobileTransactionCard
                key={transaction.id}
                transaction={transaction}
              />
            ))
          ) : (
            <MobileCard className="text-center py-8">
              <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions yet</h3>
              <p className="text-gray-600 mb-4">Start by adding your first transaction</p>
              <TouchButton onClick={() => console.log('Add transaction')}>
                Add Transaction
              </TouchButton>
            </MobileCard>
          )}
        </div>
      </div>

      {/* AI Insights */}
      <MobileCard className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <Brain className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h3 className="font-medium text-gray-900">AI Insight</h3>
            <p className="text-sm text-gray-600">Personalized for you</p>
          </div>
        </div>
        <p className="text-gray-700 mb-4">
          You're spending 15% more on groceries this month. Consider setting a budget limit of R2,200 to stay on track.
        </p>
        <TouchButton 
          size="sm" 
          className="bg-purple-600 hover:bg-purple-700"
          onClick={() => window.location.href = '/dashboard/ai-chat'}
        >
          Chat with AI Assistant
        </TouchButton>
      </MobileCard>

      {/* Navigation Shortcuts */}
      <div className="grid grid-cols-3 gap-3">
        <TouchButton 
          className="bg-gray-100 text-gray-700 hover:bg-gray-200 h-16 flex-col space-y-1"
          onClick={() => window.location.href = '/dashboard/reports'}
        >
          <BarChart3 className="w-5 h-5" />
          <span className="text-xs">Reports</span>
        </TouchButton>
        
        <TouchButton 
          className="bg-gray-100 text-gray-700 hover:bg-gray-200 h-16 flex-col space-y-1"
          onClick={() => window.location.href = '/dashboard/budgets'}
        >
          <Target className="w-5 h-5" />
          <span className="text-xs">Budgets</span>
        </TouchButton>
        
        <TouchButton 
          className="bg-gray-100 text-gray-700 hover:bg-gray-200 h-16 flex-col space-y-1"
          onClick={() => window.location.href = '/dashboard/savings'}
        >
          <PiggyBank className="w-5 h-5" />
          <span className="text-xs">Savings</span>
        </TouchButton>
      </div>
    </div>
  );
}
