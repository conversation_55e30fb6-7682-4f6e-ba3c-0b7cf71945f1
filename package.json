{"name": "bran-fintech-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@clerk/nextjs": "^6.31.10", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "class-variance-authority": "^0.7.1", "convex": "^1.27.0", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}