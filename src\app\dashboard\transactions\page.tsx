"use client";

import { useState, useMemo } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { TransactionList } from '@/components/transactions/transaction-list';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Transaction, TransactionCategory } from '@/lib/types';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Search, Filter, Download, Plus, Calendar } from 'lucide-react';

// Mock data for demonstration
const mockTransactions: Transaction[] = [
  {
    id: '1',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx1',
    amount: 450,
    currency: 'ZAR',
    description: 'Shoprite Groceries',
    category: 'groceries',
    date: Date.now() - ********,
    type: 'debit',
    merchant: 'Shoprite',
    isRecurring: false,
    tags: ['weekly'],
    createdAt: Date.now(),
  },
  {
    id: '2',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx2',
    amount: 15000,
    currency: 'ZAR',
    description: 'Salary Deposit',
    category: 'income',
    date: Date.now() - *********,
    type: 'credit',
    isRecurring: true,
    tags: ['monthly', 'salary'],
    createdAt: Date.now(),
  },
  {
    id: '3',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx3',
    amount: 85,
    currency: 'ZAR',
    description: 'Uber Trip to Sandton',
    category: 'transport',
    date: Date.now() - *********,
    type: 'debit',
    merchant: 'Uber',
    location: 'Sandton',
    isRecurring: false,
    tags: ['work'],
    createdAt: Date.now(),
  },
  {
    id: '4',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx4',
    amount: 1200,
    currency: 'ZAR',
    description: 'Eskom Electricity Bill',
    category: 'utilities',
    date: Date.now() - *********,
    type: 'debit',
    merchant: 'Eskom',
    isRecurring: true,
    tags: ['monthly', 'utilities'],
    createdAt: Date.now(),
  },
  {
    id: '5',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx5',
    amount: 250,
    currency: 'ZAR',
    description: 'Netflix Subscription',
    category: 'entertainment',
    date: Date.now() - *********,
    type: 'debit',
    merchant: 'Netflix',
    isRecurring: true,
    tags: ['monthly', 'subscription'],
    createdAt: Date.now(),
  },
  {
    id: '6',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx6',
    amount: 320,
    currency: 'ZAR',
    description: 'Woolworths Groceries',
    category: 'groceries',
    date: Date.now() - *********,
    type: 'debit',
    merchant: 'Woolworths',
    isRecurring: false,
    tags: ['weekly'],
    createdAt: Date.now(),
  },
];

const categories: TransactionCategory[] = [
  'groceries', 'transport', 'entertainment', 'utilities', 'healthcare',
  'education', 'shopping', 'dining', 'travel', 'insurance',
  'investments', 'income', 'transfers', 'fees', 'other'
];

export default function TransactionsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('all');

  const filteredTransactions = useMemo(() => {
    return mockTransactions.filter(transaction => {
      const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          transaction.merchant?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          transaction.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || transaction.category === selectedCategory;
      const matchesType = selectedType === 'all' || transaction.type === selectedType;
      
      let matchesDate = true;
      if (dateRange !== 'all') {
        const now = Date.now();
        const transactionDate = transaction.date;
        
        switch (dateRange) {
          case 'today':
            matchesDate = now - transactionDate < ********; // 24 hours
            break;
          case 'week':
            matchesDate = now - transactionDate < 604800000; // 7 days
            break;
          case 'month':
            matchesDate = now - transactionDate < *********0; // 30 days
            break;
          case 'year':
            matchesDate = now - transactionDate < 31536000000; // 365 days
            break;
        }
      }
      
      return matchesSearch && matchesCategory && matchesType && matchesDate;
    });
  }, [searchTerm, selectedCategory, selectedType, dateRange]);

  const totalAmount = filteredTransactions.reduce((sum, t) => {
    return t.type === 'credit' ? sum + t.amount : sum - t.amount;
  }, 0);

  const totalIncome = filteredTransactions
    .filter(t => t.type === 'credit')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpenses = filteredTransactions
    .filter(t => t.type === 'debit')
    .reduce((sum, t) => sum + t.amount, 0);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Transactions</h1>
            <p className="text-gray-600">Manage and analyze your financial transactions</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Transaction
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="text-sm text-gray-600">Total Income</div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(totalIncome)}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="text-sm text-gray-600">Total Expenses</div>
              <div className="text-2xl font-bold text-red-600">
                {formatCurrency(totalExpenses)}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="text-sm text-gray-600">Net Amount</div>
              <div className={`text-2xl font-bold ${totalAmount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(totalAmount)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="w-5 h-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search transactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category} value={category} className="capitalize">
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="credit">Income</SelectItem>
                  <SelectItem value="debit">Expense</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Date Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="year">This Year</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Results Summary */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {filteredTransactions.length} of {mockTransactions.length} transactions
          </div>
          <div className="flex items-center space-x-2">
            {searchTerm && (
              <Badge variant="secondary">
                Search: "{searchTerm}"
              </Badge>
            )}
            {selectedCategory !== 'all' && (
              <Badge variant="secondary" className="capitalize">
                {selectedCategory}
              </Badge>
            )}
            {selectedType !== 'all' && (
              <Badge variant="secondary" className="capitalize">
                {selectedType === 'credit' ? 'Income' : 'Expense'}
              </Badge>
            )}
            {dateRange !== 'all' && (
              <Badge variant="secondary" className="capitalize">
                {dateRange}
              </Badge>
            )}
          </div>
        </div>

        {/* Transaction List */}
        <TransactionList transactions={filteredTransactions} />
      </div>
    </DashboardLayout>
  );
}
