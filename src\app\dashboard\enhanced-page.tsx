"use client";

import { useState, useMemo } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { MobileDashboard } from '@/components/mobile/mobile-dashboard';
import { TransactionList } from '@/components/transactions/transaction-list';
import { Spending<PERSON>hart } from '@/components/charts/spending-chart';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { AIBudgetAnalyzer } from '@/lib/ai-budget';
import { AISavingsEngine } from '@/lib/ai-savings';
import { TransactionCategorizer } from '@/lib/ai-categorization';
import { formatCurrency } from '@/lib/utils';
import { Transaction } from '@/lib/types';
import {
  CreditCard,
  TrendingUp,
  TrendingDown,
  Target,
  AlertCircle,
  Plus,
  Banknote,
  Brain,
  Shield,
  PiggyBank,
  Activity,
  BarChart3,
  Zap,
  Calendar,
  DollarSign,
  Wallet,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw
} from 'lucide-react';
import { BankAccountsList } from '@/components/bank-accounts/BankAccountsList';

// Enhanced mock data for comprehensive analytics
const mockTransactions: Transaction[] = [
  {
    id: '1',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx1',
    amount: 15000,
    currency: 'ZAR',
    description: 'Salary Deposit',
    category: 'income',
    date: Date.now() - *********,
    type: 'credit',
    isRecurring: true,
    tags: ['monthly', 'salary'],
    createdAt: Date.now(),
  },
  {
    id: '2',
    userId: 'user1',
    accountId: 'acc1',
    transactionId: 'tx2',
    amount: 450,
    currency: 'ZAR',
    description: 'Shoprite Groceries',
    category: 'groceries',
    date: Date.now() - ********,
    type: 'debit',
    merchant: 'Shoprite',
    isRecurring: false,
    tags: ['weekly'],
    createdAt: Date.now(),
  },
  // Add more comprehensive mock data
  ...Array.from({ length: 25 }, (_, i) => ({
    id: `mock-${i}`,
    userId: 'user1',
    accountId: 'acc1',
    transactionId: `tx-mock-${i}`,
    amount: Math.random() * 1000 + 50,
    currency: 'ZAR',
    description: `Transaction ${i}`,
    category: ['groceries', 'transport', 'entertainment', 'utilities', 'dining', 'shopping'][Math.floor(Math.random() * 6)] as any,
    date: Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
    type: 'debit' as const,
    isRecurring: Math.random() > 0.8,
    tags: [],
    createdAt: Date.now(),
  }))
];

const mockAccounts = [
  { id: '1', name: 'Cheque Account', balance: 12500, type: 'checking' },
  { id: '2', name: 'Savings Account', balance: 45000, type: 'savings' },
  { id: '3', name: 'Credit Card', balance: -2500, type: 'credit' },
];

export default function EnhancedDashboard() {
  const [timeframe, setTimeframe] = useState<'week' | 'month' | 'year'>('month');
  const [showAIInsights, setShowAIInsights] = useState(false);

  const monthlyIncome = 15000;
  const currentSavings = 45000;

  // AI Analysis
  const budgetAnalysis = useMemo(() => {
    return AIBudgetAnalyzer.analyzeBudget(mockTransactions, 3);
  }, []);

  const savingsAnalysis = useMemo(() => {
    return AISavingsEngine.analyzeSavings(mockTransactions, [], monthlyIncome, currentSavings);
  }, []);

  const spendingAnalysis = useMemo(() => {
    return TransactionCategorizer.analyzeSpendingPatterns(mockTransactions);
  }, []);

  // Calculate key metrics
  const totalBalance = mockAccounts.reduce((sum, acc) => sum + acc.balance, 0);
  const totalIncome = mockTransactions.filter(t => t.type === 'credit').reduce((sum, t) => sum + t.amount, 0);
  const totalExpenses = mockTransactions.filter(t => t.type === 'debit').reduce((sum, t) => sum + t.amount, 0);
  const netWorth = totalBalance + currentSavings;
  
  // Recent transactions for display
  const recentTransactions = mockTransactions
    .sort((a, b) => b.date - a.date)
    .slice(0, 5);

  // Top spending categories
  const topCategories = Object.entries(spendingAnalysis.categoryBreakdown)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  return (
    <DashboardLayout>
      {/* Mobile Dashboard */}
      <div className="lg:hidden">
        <MobileDashboard />
      </div>

      {/* Desktop Dashboard */}
      <div className="hidden lg:block space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Financial Dashboard</h1>
            <p className="text-gray-600">AI-powered insights into your financial health</p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowAIInsights(!showAIInsights)}
            >
              <Brain className="w-4 h-4 mr-2" />
              AI Insights
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Quick Action
            </Button>
          </div>
        </div>

        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <a href="/dashboard/bank-accounts">
            <Card className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Wallet className="w-6 h-6 text-blue-600" />
                  </div>
                  <Badge variant="outline" className="text-blue-600">
                    <TrendingUp className="w-3 h-3 mr-1" />
                    +2.5%
                  </Badge>
                </div>
                <div className="text-2xl font-bold text-gray-900">{formatCurrency(totalBalance)}</div>
                <div className="text-sm text-gray-600">Total Balance</div>
                <div className="mt-2 text-xs text-blue-600 hover:underline">View all accounts →</div>
              </CardContent>
            </Card>
          </a>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <ArrowUpRight className="w-6 h-6 text-green-600" />
                </div>
                <Badge variant="outline" className="text-green-600">
                  This month
                </Badge>
              </div>
              <div className="text-2xl font-bold text-gray-900">{formatCurrency(totalIncome)}</div>
              <div className="text-sm text-gray-600">Total Income</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <ArrowDownRight className="w-6 h-6 text-red-600" />
                </div>
                <Badge variant="outline" className="text-red-600">
                  This month
                </Badge>
              </div>
              <div className="text-2xl font-bold text-gray-900">{formatCurrency(totalExpenses)}</div>
              <div className="text-sm text-gray-600">Total Expenses</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <PiggyBank className="w-6 h-6 text-purple-600" />
                </div>
                <Badge variant="outline" className="text-purple-600">
                  {(savingsAnalysis.currentSavingsRate * 100).toFixed(1)}%
                </Badge>
              </div>
              <div className="text-2xl font-bold text-gray-900">{formatCurrency(currentSavings)}</div>
              <div className="text-sm text-gray-600">Savings</div>
            </CardContent>
          </Card>
        </div>

        {/* AI Insights Panel */}
        {showAIInsights && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="w-5 h-5 text-blue-600" />
                <span>AI Financial Health Analysis</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-3 gap-4">
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <Shield className="w-4 h-4 text-green-600" />
                    <span className="font-semibold text-green-900">Financial Health</span>
                  </div>
                  <div className="text-2xl font-bold text-green-600">Good</div>
                  <div className="text-sm text-green-700">
                    Savings rate: {(savingsAnalysis.currentSavingsRate * 100).toFixed(1)}%
                  </div>
                </div>
                
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <Target className="w-4 h-4 text-blue-600" />
                    <span className="font-semibold text-blue-900">Budget Status</span>
                  </div>
                  <div className="text-2xl font-bold text-blue-600">On Track</div>
                  <div className="text-sm text-blue-700">
                    {budgetAnalysis.recommendations.filter(r => r.priority === 'high').length} high priority items
                  </div>
                </div>
                
                <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <Zap className="w-4 h-4 text-orange-600" />
                    <span className="font-semibold text-orange-900">Savings Potential</span>
                  </div>
                  <div className="text-2xl font-bold text-orange-600">
                    {formatCurrency(savingsAnalysis.monthlySavingsPotential)}
                  </div>
                  <div className="text-sm text-orange-700">Monthly potential</div>
                </div>
              </div>
              
              {budgetAnalysis.insights.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-semibold text-gray-900">Key Insights:</h4>
                  {budgetAnalysis.insights.slice(0, 3).map((insight, index) => (
                    <div key={index} className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                      <p className="text-sm text-yellow-800">{insight}</p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Charts and Analytics */}
        <div className="grid lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="w-5 h-5" />
                <span>Spending by Category</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <SpendingChart 
                data={topCategories.map(([category, amount]) => ({ category, amount }))} 
                type="pie"
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-5 h-5" />
                <span>Account Balances</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {mockAccounts.map((account) => (
                <div key={account.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                      account.type === 'checking' ? 'bg-blue-100' :
                      account.type === 'savings' ? 'bg-green-100' : 'bg-red-100'
                    }`}>
                      <CreditCard className={`w-5 h-5 ${
                        account.type === 'checking' ? 'text-blue-600' :
                        account.type === 'savings' ? 'text-green-600' : 'text-red-600'
                      }`} />
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{account.name}</div>
                      <div className="text-sm text-gray-600 capitalize">{account.type}</div>
                    </div>
                  </div>
                  <div className={`text-lg font-bold ${
                    account.balance >= 0 ? 'text-gray-900' : 'text-red-600'
                  }`}>
                    {formatCurrency(account.balance)}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Recent Transactions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Recent Transactions</span>
              <Button variant="outline" size="sm">
                View All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <TransactionList transactions={recentTransactions} limit={5} />
          </CardContent>
        </Card>
      </div>
      {/* End Desktop Dashboard */}
    </DashboardLayout>
  );
}
