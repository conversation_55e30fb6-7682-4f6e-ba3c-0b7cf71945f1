import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from 'next/font/google';
import "./globals.css";
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { ConvexClientProvider } from './providers';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

export const metadata: Metadata = {
  title: "AI Fintech Assistant",
  description: "AI-powered personal finance and budgeting assistant for South African users",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body>
        <ConvexClientProvider>
          {children}
        </ConvexClientProvider>
      </body>
    </html>
  );
}
