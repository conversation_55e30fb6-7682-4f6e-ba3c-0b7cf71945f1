import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    clerkId: v.string(),
    email: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_clerk_id", ["clerkId"]),

  bankAccounts: defineTable({
    userId: v.id("users"),
    accountId: v.string(), // Stitch account ID
    accountName: v.string(),
    accountType: v.union(v.literal("checking"), v.literal("savings"), v.literal("credit")),
    bankName: v.string(),
    balance: v.number(),
    currency: v.string(),
    isActive: v.boolean(),
    lastSynced: v.number(),
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  }).index("by_user", ["userId"])
   .index("by_account", ["accountId"]),

  transactions: defineTable({
    userId: v.id("users"),
    accountId: v.id("bankAccounts"),
    transactionId: v.string(), // Stitch transaction ID
    amount: v.number(),
    currency: v.string(),
    description: v.string(),
    category: v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    ),
    subcategory: v.optional(v.string()),
    date: v.number(),
    type: v.union(v.literal("debit"), v.literal("credit")),
    merchant: v.optional(v.string()),
    location: v.optional(v.string()),
    isRecurring: v.boolean(),
    tags: v.array(v.string()),
    createdAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_account", ["accountId"])
    .index("by_date", ["date"]),

  budgets: defineTable({
    userId: v.id("users"),
    name: v.string(),
    category: v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    ),
    amount: v.number(),
    spent: v.number(),
    period: v.union(v.literal("weekly"), v.literal("monthly"), v.literal("yearly")),
    startDate: v.number(),
    endDate: v.number(),
    isActive: v.boolean(),
    alerts: v.object({
      enabled: v.boolean(),
      threshold: v.number(), // percentage
    }),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_user", ["userId"]),

  savingsGoals: defineTable({
    userId: v.id("users"),
    name: v.string(),
    description: v.optional(v.string()),
    targetAmount: v.number(),
    currentAmount: v.number(),
    targetDate: v.number(),
    category: v.union(
      v.literal("emergency"),
      v.literal("vacation"),
      v.literal("purchase"),
      v.literal("investment"),
      v.literal("other")
    ),
    isActive: v.boolean(),
    autoSave: v.object({
      enabled: v.boolean(),
      amount: v.number(),
      frequency: v.union(v.literal("daily"), v.literal("weekly"), v.literal("monthly")),
    }),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_user", ["userId"]),

  financialInsights: defineTable({
    userId: v.id("users"),
    type: v.union(
      v.literal("spending_pattern"),
      v.literal("budget_recommendation"),
      v.literal("savings_opportunity"),
      v.literal("bill_prediction")
    ),
    title: v.string(),
    description: v.string(),
    impact: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    category: v.optional(v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    )),
    actionable: v.boolean(),
    action: v.optional(v.object({
      type: v.union(
        v.literal("create_budget"),
        v.literal("adjust_budget"),
        v.literal("set_savings_goal"),
        v.literal("review_subscription")
      ),
      data: v.any(),
    })),
    isRead: v.boolean(),
    createdAt: v.number(),
  }).index("by_user", ["userId"]),

  stokvelGroups: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    adminId: v.id("users"),
    contributionAmount: v.number(),
    contributionFrequency: v.union(v.literal("weekly"), v.literal("monthly")),
    payoutSchedule: v.union(v.literal("rotating"), v.literal("fixed_date")),
    nextPayoutDate: v.number(),
    totalContributions: v.number(),
    isActive: v.boolean(),
    createdAt: v.number(),
  }).index("by_admin", ["adminId"]),

  stokvelMembers: defineTable({
    groupId: v.id("stokvelGroups"),
    userId: v.id("users"),
    joinedAt: v.number(),
    totalContributed: v.number(),
    lastContribution: v.number(),
    payoutPosition: v.number(),
    isActive: v.boolean(),
  })
    .index("by_group", ["groupId"])
    .index("by_user", ["userId"]),

  bills: defineTable({
    userId: v.id("users"),
    name: v.string(),
    category: v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    ),
    amount: v.number(),
    dueDate: v.number(),
    frequency: v.union(v.literal("monthly"), v.literal("quarterly"), v.literal("annually")),
    isAutoPay: v.boolean(),
    accountId: v.optional(v.id("bankAccounts")),
    predictedAmount: v.optional(v.number()),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_user", ["userId"]),

  aiRecommendations: defineTable({
    userId: v.id("users"),
    type: v.union(
      v.literal("budget"),
      v.literal("savings"),
      v.literal("investment"),
      v.literal("debt_management")
    ),
    title: v.string(),
    description: v.string(),
    confidence: v.number(), // 0-1
    potentialSavings: v.optional(v.number()),
    timeframe: v.string(),
    steps: v.array(v.string()),
    isImplemented: v.boolean(),
    createdAt: v.number(),
  }).index("by_user", ["userId"]),
});
