import { NextRequest, NextResponse } from 'next/server';
import { stitchAPI } from '@/lib/stitch';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const state = searchParams.get('state'); // This should be the userId
    const error = searchParams.get('error');

    if (error) {
      // Redirect to dashboard with error
      return NextResponse.redirect(
        new URL(`/dashboard?error=${encodeURIComponent(error)}`, request.url)
      );
    }

    if (!code || !state) {
      return NextResponse.redirect(
        new URL('/dashboard?error=missing_parameters', request.url)
      );
    }

    try {
      const tokenResponse = await stitchAPI.exchangeCodeForToken(code);
      
      // In a real app, you'd store this token securely in your database
      // For now, we'll redirect with a success message
      return NextResponse.redirect(
        new URL('/dashboard?success=bank_connected', request.url)
      );
    } catch (tokenError) {
      console.error('Token exchange error:', tokenError);
      return NextResponse.redirect(
        new URL('/dashboard?error=token_exchange_failed', request.url)
      );
    }
  } catch (error) {
    console.error('Stitch callback error:', error);
    return NextResponse.redirect(
      new URL('/dashboard?error=callback_failed', request.url)
    );
  }
}
