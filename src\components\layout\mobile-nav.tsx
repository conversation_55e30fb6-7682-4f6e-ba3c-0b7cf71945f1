"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useNotifications } from '@/hooks/useRealtime';
import {
  LayoutDashboard,
  CreditCard,
  Receipt,
  TrendingUp,
  Target,
  Brain,
  BarChart3,
  Bell,
  BookOpen,
  Users,
  Settings,
  Plus,
  Scan,
  Send,
  Wallet,
} from 'lucide-react';

const mainNavigation = [
  { name: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
  { name: "Accounts", href: "/dashboard/bank-accounts", icon: CreditCard },
  { name: "Transactions", href: "/dashboard/transactions", icon: Receipt },
  { name: "Budgets", href: "/dashboard/budgets", icon: TrendingUp },
  { name: "AI Chat", href: "/dashboard/ai-chat", icon: Brain },
];

const quickActions = [
  { name: "Add Transaction", icon: Plus, action: "add-transaction" },
  { name: "Scan Receipt", icon: Scan, action: "scan-receipt" },
  { name: "Send Money", icon: Send, action: "send-money" },
  { name: "Pay Bill", icon: Wallet, action: "pay-bill" },
];

const secondaryNavigation = [
  { name: "Reports", href: "/dashboard/reports", icon: BarChart3 },
  { name: "Savings", href: "/dashboard/savings", icon: Target },
  { name: "Notifications", href: "/dashboard/notifications", icon: Bell },
  { name: "Learn", href: "/dashboard/learn", icon: BookOpen },
  { name: "Stokvels", href: "/dashboard/stokvels", icon: Users },
  { name: "Settings", href: "/dashboard/settings", icon: Settings },
];

interface MobileNavProps {
  className?: string;
}

export function MobileNav({ className }: MobileNavProps) {
  const pathname = usePathname();
  const { unreadCount } = useNotifications();
  const [showQuickActions, setShowQuickActions] = useState(false);

  // Close quick actions when route changes
  useEffect(() => {
    setShowQuickActions(false);
  }, [pathname]);

  const handleQuickAction = (action: string) => {
    setShowQuickActions(false);
    
    switch (action) {
      case 'add-transaction':
        // Open add transaction modal
        console.log('Add transaction');
        break;
      case 'scan-receipt':
        // Open camera for receipt scanning
        console.log('Scan receipt');
        break;
      case 'send-money':
        // Open send money flow
        console.log('Send money');
        break;
      case 'pay-bill':
        // Open bill payment flow
        console.log('Pay bill');
        break;
    }
  };

  return (
    <>
      {/* Quick Actions Overlay */}
      {showQuickActions && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setShowQuickActions(false)}
        >
          <div className="absolute bottom-20 left-4 right-4">
            <div className="bg-white rounded-2xl p-4 shadow-xl">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-2 gap-3">
                {quickActions.map((action) => {
                  const Icon = action.icon;
                  return (
                    <button
                      key={action.action}
                      onClick={() => handleQuickAction(action.action)}
                      className="flex flex-col items-center space-y-2 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors"
                    >
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <Icon className="w-6 h-6 text-blue-600" />
                      </div>
                      <span className="text-sm font-medium text-gray-900">{action.name}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bottom Navigation */}
      <nav className={cn(
        "fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 lg:hidden z-30",
        className
      )}>
        <div className="grid grid-cols-5 h-16">
          {mainNavigation.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;
            
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex flex-col items-center justify-center space-y-1 text-xs font-medium transition-colors",
                  isActive
                    ? "text-blue-600"
                    : "text-gray-600 hover:text-gray-900"
                )}
              >
                <Icon className={cn("w-5 h-5", isActive && "text-blue-600")} />
                <span className={cn("text-xs", isActive && "text-blue-600")}>
                  {item.name}
                </span>
              </Link>
            );
          })}
        </div>
      </nav>

      {/* Floating Action Button */}
      <button
        onClick={() => setShowQuickActions(true)}
        className="fixed bottom-20 right-4 w-14 h-14 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center lg:hidden z-30 transition-colors"
      >
        <Plus className="w-6 h-6" />
      </button>
    </>
  );
}

// Mobile-optimized drawer navigation for secondary items
export function MobileDrawer({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) {
  const pathname = usePathname();
  const { unreadCount } = useNotifications();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div className="fixed inset-y-0 right-0 w-80 max-w-full bg-white shadow-xl">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <h2 className="text-lg font-semibold text-gray-900">Menu</h2>
            <button
              onClick={onClose}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100"
            >
              <Plus className="w-5 h-5 rotate-45" />
            </button>
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-1">
              {secondaryNavigation.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;
                const showBadge = item.name === 'Notifications' && unreadCount > 0;
                
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={onClose}
                    className={cn(
                      "flex items-center space-x-3 px-3 py-3 rounded-lg text-sm font-medium transition-colors",
                      isActive
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-600 hover:bg-gray-100"
                    )}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="flex-1">{item.name}</span>
                    {showBadge && (
                      <span className="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        {unreadCount > 9 ? '9+' : unreadCount}
                      </span>
                    )}
                  </Link>
                );
              })}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t p-4">
            <div className="text-xs text-gray-500 text-center">
              AI Fintech Assistant v1.0
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Touch-friendly button component
export function TouchButton({ 
  children, 
  className, 
  size = 'default',
  ...props 
}: {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'default' | 'lg';
} & React.ButtonHTMLAttributes<HTMLButtonElement>) {
  const sizeClasses = {
    sm: 'h-10 px-4 text-sm',
    default: 'h-12 px-6 text-base',
    lg: 'h-14 px-8 text-lg',
  };

  return (
    <button
      className={cn(
        "inline-flex items-center justify-center rounded-lg font-medium transition-colors",
        "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
        "active:scale-95 transition-transform",
        "bg-blue-600 text-white hover:bg-blue-700",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
}

// Mobile-optimized card component
export function MobileCard({ 
  children, 
  className,
  padding = 'default',
  ...props 
}: {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'default' | 'lg';
} & React.HTMLAttributes<HTMLDivElement>) {
  const paddingClasses = {
    sm: 'p-3',
    default: 'p-4',
    lg: 'p-6',
  };

  return (
    <div
      className={cn(
        "bg-white rounded-xl shadow-sm border border-gray-200",
        "active:shadow-md transition-shadow",
        paddingClasses[padding],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}
