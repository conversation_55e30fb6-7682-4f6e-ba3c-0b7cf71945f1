import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const createTransaction = mutation({
  args: {
    userId: v.id("users"),
    accountId: v.id("bankAccounts"),
    transactionId: v.string(),
    amount: v.number(),
    currency: v.string(),
    description: v.string(),
    category: v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    ),
    subcategory: v.optional(v.string()),
    date: v.number(),
    type: v.union(v.literal("debit"), v.literal("credit")),
    merchant: v.optional(v.string()),
    location: v.optional(v.string()),
    isRecurring: v.optional(v.boolean()),
    tags: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const transactionId = await ctx.db.insert("transactions", {
      ...args,
      isRecurring: args.isRecurring ?? false,
      tags: args.tags ?? [],
      createdAt: Date.now(),
    });

    return transactionId;
  },
});

export const getTransactionsByUser = query({
  args: { 
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;
    
    return await ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(limit);
  },
});

export const getTransactionsByAccount = query({
  args: { 
    accountId: v.id("bankAccounts"),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit ?? 50;
    
    return await ctx.db
      .query("transactions")
      .withIndex("by_account", (q) => q.eq("accountId", args.accountId))
      .order("desc")
      .take(limit);
  },
});

export const getTransactionsByDateRange = query({
  args: {
    userId: v.id("users"),
    startDate: v.number(),
    endDate: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => 
        q.and(
          q.gte(q.field("date"), args.startDate),
          q.lte(q.field("date"), args.endDate)
        )
      )
      .order("desc")
      .collect();
  },
});

export const getTransactionsByCategory = query({
  args: {
    userId: v.id("users"),
    category: v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    ),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("category"), args.category));

    if (args.startDate && args.endDate) {
      query = query.filter((q) => 
        q.and(
          q.gte(q.field("date"), args.startDate!),
          q.lte(q.field("date"), args.endDate!)
        )
      );
    }

    return await query.order("desc").collect();
  },
});

export const updateTransactionCategory = mutation({
  args: {
    transactionId: v.id("transactions"),
    category: v.union(
      v.literal("groceries"),
      v.literal("transport"),
      v.literal("entertainment"),
      v.literal("utilities"),
      v.literal("healthcare"),
      v.literal("education"),
      v.literal("shopping"),
      v.literal("dining"),
      v.literal("travel"),
      v.literal("insurance"),
      v.literal("investments"),
      v.literal("income"),
      v.literal("transfers"),
      v.literal("fees"),
      v.literal("other")
    ),
    subcategory: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.transactionId, {
      category: args.category,
      subcategory: args.subcategory,
    });
  },
});

export const getSpendingByCategory = query({
  args: {
    userId: v.id("users"),
    startDate: v.number(),
    endDate: v.number(),
  },
  handler: async (ctx, args) => {
    const transactions = await ctx.db
      .query("transactions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => 
        q.and(
          q.gte(q.field("date"), args.startDate),
          q.lte(q.field("date"), args.endDate),
          q.eq(q.field("type"), "debit")
        )
      )
      .collect();

    const categoryTotals: Record<string, number> = {};
    
    transactions.forEach((transaction) => {
      const category = transaction.category;
      categoryTotals[category] = (categoryTotals[category] || 0) + Math.abs(transaction.amount);
    });

    return Object.entries(categoryTotals).map(([category, amount]) => ({
      category,
      amount,
    }));
  },
});
