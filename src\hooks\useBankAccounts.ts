import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import type { BankAccount as BankAccountType } from '@/convex/bankAccounts';
import { useMemo, useCallback, useState } from 'react';

// Re-export the BankAccount type from the Convex file
export type { BankAccountType as BankAccount };

interface UseBankAccountsReturn {
  // Data
  accounts: BankAccountType[];
  totalBalance: number;
  isLoading: boolean;
  error: Error | null;
  
  // Helpers
  getAccount: (accountId: Id<"bankAccounts">) => BankAccountType | undefined;
  getAccountsByType: (type: "checking" | "savings" | "credit") => BankAccountType[];
  
  // Mutations
  createAccount: (data: Omit<BankAccountType, '_id' | '_creationTime' | 'isActive' | 'lastSynced' | 'createdAt' | 'updatedAt' | 'userId'>) => Promise<void>;
  updateBalance: (accountId: Id<"bankAccounts">, balance: number) => Promise<void>;
  deactivateAccount: (accountId: Id<"bankAccounts">, force?: boolean) => Promise<void>;
}

export function useBankAccounts(userId: Id<"users"> | null): UseBankAccountsReturn {
  // Get all active bank accounts for the user
  const accountsQuery = useQuery(api.bankAccounts.getBankAccountsByUser, userId ? { userId } : 'skip');
  
  // Get total balance
  const totalBalanceQuery = useQuery(api.bankAccounts.getTotalBalance, userId ? { userId } : 'skip');
  
  // Memoize accounts to prevent unnecessary re-renders
  const accounts = useMemo(() => accountsQuery || [], [accountsQuery]);
  
  // Memoize total balance with default value
  const totalBalance = useMemo(() => totalBalanceQuery?.total || 0, [totalBalanceQuery]);
  
  // Mutations
  const createAccountMutation = useMutation(api.bankAccounts.createBankAccount);
  const updateBalanceMutation = useMutation(api.bankAccounts.updateBankAccountBalance);
  const deactivateAccountMutation = useMutation(api.bankAccounts.deactivateBankAccount);
  
  // State for error handling
  const [error, setError] = useState<Error | null>(null);
  
  // Helper functions
  const getAccount = useCallback((accountId: Id<"bankAccounts">) => {
    return accounts.find((account) => account._id === accountId);
  }, [accounts]);
  
  const getAccountsByType = useCallback((type: "checking" | "savings" | "credit") => {
    return accounts.filter((account) => account.accountType === type);
  }, [accounts]);
  
  // Create a new bank account
  const createAccount = useCallback(async (data: Omit<BankAccountType, '_id' | '_creationTime' | 'isActive' | 'lastSynced' | 'createdAt' | 'updatedAt' | 'userId'>) => {
    if (!userId) {
      throw new Error('User ID is required to create an account');
    }
    
    try {
      await createAccountMutation({
        ...data,
        userId,
      });
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to create account'));
      throw err;
    }
  }, [createAccountMutation, userId]);
  
  // Update account balance
  const updateBalance = useCallback(async (accountId: Id<"bankAccounts">, balance: number) => {
    try {
      await updateBalanceMutation({
        accountId,
        balance,
      });
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update balance'));
      throw err;
    }
  }, [updateBalanceMutation]);
  
  // Deactivate an account
  const deactivateAccount = useCallback(async (accountId: Id<"bankAccounts">, force: boolean = false) => {
    try {
      await deactivateAccountMutation({
        accountId,
        force,
      });
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to deactivate account'));
      throw err;
    }
  }, [deactivateAccountMutation]);
  
  return {
    // Data
    accounts,
    totalBalance,
    isLoading: accountsQuery === undefined || totalBalanceQuery === undefined,
    error,
    
    // Helpers
    getAccount,
    getAccountsByType,
    
    // Mutations
    createAccount,
    updateBalance,
    deactivateAccount,
  };
}

interface UseBankAccountReturn {
  account: BankAccountType | undefined;
  isLoading: boolean;
  error: Error | null;
  updateBalance: (balance: number) => Promise<void>;
  deactivate: (force?: boolean) => Promise<void>;
}

// Hook for a single account
export function useBankAccount(accountId: Id<"bankAccounts"> | null): UseBankAccountReturn {
  const accountQuery = useQuery(
    api.bankAccounts.getBankAccount, 
    accountId ? { accountId } : 'skip'
  ) as BankAccountType | undefined;
  
  const updateBalanceMutation = useMutation(api.bankAccounts.updateBankAccountBalance);
  const deactivateAccountMutation = useMutation(api.bankAccounts.deactivateBankAccount);
  
  // State for error handling
  const [error, setError] = useState<Error | null>(null);
  
  // Update account balance
  const updateAccountBalance = useCallback(async (balance: number) => {
    if (!accountId) throw new Error('No account ID provided');
    
    try {
      await updateBalanceMutation({
        accountId,
        balance,
      });
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update balance'));
      throw err;
    }
  }, [accountId, updateBalanceMutation]);
  
  // Deactivate account
  const deactivate = useCallback(async (force: boolean = false) => {
    if (!accountId) throw new Error('No account ID provided');
    
    try {
      await deactivateAccountMutation({
        accountId,
        force,
      });
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to deactivate account'));
      throw err;
    }
  }, [accountId, deactivateAccountMutation]);
  
  return {
    account: accountQuery,
    isLoading: accountQuery === undefined,
    error,
    updateBalance: updateAccountBalance,
    deactivate,
  };
}
