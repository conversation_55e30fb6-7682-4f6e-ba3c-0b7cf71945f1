import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { stitchAPI } from '@/lib/stitch';

export async function GET(request: NextRequest) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const authUrl = await stitchAPI.getAuthorizationUrl(userId);
    
    return NextResponse.json({ authUrl });
  } catch (error) {
    console.error('Stitch auth error:', error);
    return NextResponse.json(
      { error: 'Failed to generate authorization URL' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { code } = await request.json();
    
    if (!code) {
      return NextResponse.json({ error: 'Authorization code required' }, { status: 400 });
    }

    const tokenResponse = await stitchAPI.exchangeCodeForToken(code);
    
    // In a real app, you'd store this token securely associated with the user
    // For now, we'll return it to the client (not recommended for production)
    return NextResponse.json({ 
      success: true,
      token: tokenResponse.access_token,
      expiresIn: tokenResponse.expires_in
    });
  } catch (error) {
    console.error('Stitch token exchange error:', error);
    return NextResponse.json(
      { error: 'Failed to exchange authorization code' },
      { status: 500 }
    );
  }
}
