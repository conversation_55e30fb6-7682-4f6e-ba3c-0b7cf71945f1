import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { stitchAPI } from '@/lib/stitch';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '../../../../../convex/_generated/api';

const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get or create user in Convex
    let user = await convex.query(api.userPreferences.getUserByClerkId, {
      clerkId: userId,
    });

    if (!user) {
      // This shouldn't happen if the user is properly authenticated
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Update sync status to pending
    await convex.mutation(api.syncStatus.updateSyncStatus, {
      userId: user._id,
      provider: 'stitch',
      status: 'pending',
    });

    const authUrl = await stitchAPI.getAuthorizationUrl(userId);

    return NextResponse.json({ authUrl });
  } catch (error) {
    console.error('Stitch auth error:', error);
    return NextResponse.json(
      { error: 'Failed to generate authorization URL' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, ...data } = body;

    // Get user from Convex
    const user = await convex.query(api.userPreferences.getUserByClerkId, {
      clerkId: userId,
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    switch (action) {
      case 'sync_accounts':
        return await syncAccounts(user._id, data.userToken);

      case 'sync_transactions':
        return await syncTransactions(user._id, data.userToken, data.accountId);

      case 'refresh_balance':
        return await refreshBalance(user._id, data.userToken, data.accountId);

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Stitch API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function syncAccounts(userId: string, userToken: string) {
  try {
    const stitchAccounts = await stitchAPI.getAccounts(userToken);

    const results = [];
    for (const stitchAccount of stitchAccounts) {
      const accountId = await convex.mutation(api.bankAccounts.createBankAccount, {
        userId: userId as any,
        accountId: stitchAccount.id,
        accountName: stitchAccount.name,
        accountType: stitchAccount.accountType,
        bankName: stitchAccount.bankName,
        balance: stitchAccount.balance,
        currency: stitchAccount.currency,
      });
      results.push(accountId);
    }

    await convex.mutation(api.syncStatus.updateSyncStatus, {
      userId: userId as any,
      provider: 'stitch',
      status: 'success',
    });

    return NextResponse.json({
      success: true,
      accountsCreated: results.length,
      accounts: stitchAccounts
    });
  } catch (error) {
    await convex.mutation(api.syncStatus.updateSyncStatus, {
      userId: userId as any,
      provider: 'stitch',
      status: 'error',
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
}

async function syncTransactions(userId: string, userToken: string, accountId?: string) {
  try {
    const accounts = await convex.query(api.bankAccounts.getBankAccountsByUser, {
      userId: userId as any,
    });

    let totalTransactions = 0;

    for (const account of accounts) {
      if (accountId && account._id !== accountId) continue;

      const transactions = await stitchAPI.getTransactions(userToken, account.accountId, 100);

      const transactionData = transactions.map(transaction => ({
        userId: userId as any,
        accountId: account._id,
        transactionId: transaction.id,
        amount: transaction.amount,
        currency: transaction.currency,
        description: transaction.description,
        category: categorizeTransaction(transaction.description, transaction.merchant) as any,
        date: new Date(transaction.date).getTime(),
        type: transaction.type,
        merchant: transaction.merchant,
      }));

      if (transactionData.length > 0) {
        const result = await convex.mutation(api.transactions.bulkCreateTransactions, {
          transactions: transactionData,
        });
        totalTransactions += result.created;
      }
    }

    return NextResponse.json({
      success: true,
      transactionsCreated: totalTransactions
    });
  } catch (error) {
    throw error;
  }
}

async function refreshBalance(userId: string, userToken: string, accountId: string) {
  try {
    const account = await convex.query(api.bankAccounts.getBankAccount, {
      accountId: accountId as any,
    });

    if (!account) {
      return NextResponse.json({ error: 'Account not found' }, { status: 404 });
    }

    const balanceData = await stitchAPI.refreshAccountBalance(userToken, account.accountId);

    await convex.mutation(api.bankAccounts.updateBankAccountBalance, {
      accountId: accountId as any,
      balance: balanceData.balance,
    });

    return NextResponse.json({
      success: true,
      balance: balanceData.balance,
      currency: balanceData.currency
    });
  } catch (error) {
    throw error;
  }
}

// Helper function to categorize transactions
function categorizeTransaction(description: string, merchant?: string): string {
  const desc = description.toLowerCase();
  const merch = merchant?.toLowerCase() || '';

  if (desc.includes('shoprite') || desc.includes('pick n pay') || desc.includes('checkers') ||
      desc.includes('woolworths') || desc.includes('spar') || merch.includes('grocery')) {
    return 'groceries';
  }

  if (desc.includes('uber') || desc.includes('bolt') || desc.includes('taxi') ||
      desc.includes('petrol') || desc.includes('fuel') || desc.includes('gautrain')) {
    return 'transport';
  }

  if (desc.includes('eskom') || desc.includes('city power') || desc.includes('water') ||
      desc.includes('electricity') || desc.includes('municipal')) {
    return 'utilities';
  }

  return 'other';
}
