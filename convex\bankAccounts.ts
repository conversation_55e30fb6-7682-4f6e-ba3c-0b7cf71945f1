import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Type for bank account data
export type BankAccount = {
  userId: Id<"users">;
  accountId: string;
  accountName: string;
  accountType: "checking" | "savings" | "credit";
  bankName: string;
  balance: number;
  currency: string;
  isActive: boolean;
  lastSynced: number;
  createdAt: number;
  updatedAt?: number;
};

export const createBankAccount = mutation({
  args: {
    userId: v.id("users"),
    accountId: v.string(),
    accountName: v.string(),
    accountType: v.union(v.literal("checking"), v.literal("savings"), v.literal("credit")),
    bankName: v.string(),
    balance: v.number(),
    currency: v.string(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const identity = await ctx.auth.getUserIdentity();
    
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if account already exists
    const existingAccount = await ctx.db
      .query("bankAccounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("accountId"), args.accountId))
      .first();

    if (existingAccount) {
      // Update existing account
      await ctx.db.patch(existingAccount._id, {
        ...args,
        isActive: true,
        lastSynced: now,
        updatedAt: now,
      });
      return existingAccount._id;
    }
    
    // Create new account
    const accountId = await ctx.db.insert("bankAccounts", {
      ...args,
      isActive: true,
      lastSynced: now,
      createdAt: now,
      updatedAt: now,
    });

    return accountId;
  },
});

export const getBankAccountsByUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const accounts = await ctx.db
      .query("bankAccounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .collect();

    return accounts.sort((a, b) => b.balance - a.balance); // Sort by balance (highest first)
  },
});

export const getBankAccount = query({
  args: { accountId: v.id("bankAccounts") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const account = await ctx.db.get(args.accountId);
    
    if (!account || !account.isActive) {
      throw new Error("Account not found");
    }

    return account;
  },
});

export const updateBankAccountBalance = mutation({
  args: {
    accountId: v.id("bankAccounts"),
    balance: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const account = await ctx.db.get(args.accountId);
    
    if (!account || !account.isActive) {
      throw new Error("Account not found or inactive");
    }

    const now = Date.now();
    await ctx.db.patch(args.accountId, {
      balance: args.balance,
      lastSynced: now,
      updatedAt: now,
    });

    return { success: true };
  },
});

export const getTotalBalance = query({
  args: { 
    userId: v.id("users"),
    currency: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let query = ctx.db
      .query("bankAccounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true));

    // Filter by currency if specified
    if (args.currency) {
      query = query.filter((q) => q.eq(q.field("currency"), args.currency));
    }

    const accounts = await query.collect();

    return {
      total: accounts.reduce((total, account) => {
        if (account.accountType === "credit") {
          return total - Math.abs(account.balance);
        }
        return total + account.balance;
      }, 0),
      currency: args.currency || 'ZAR', // Default to ZAR if no currency specified
      lastUpdated: Date.now(),
      accountCount: accounts.length,
    };
  },
});

export const deactivateBankAccount = mutation({
  args: { 
    accountId: v.id("bankAccounts"),
    force: v.optional(v.boolean())
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const account = await ctx.db.get(args.accountId);
    
    if (!account) {
      throw new Error("Account not found");
    }

    if (!account.isActive) {
      if (!args.force) {
        throw new Error("Account is already inactive");
      }
      return { success: true, message: "Account was already inactive" };
    }

    // Check if there are pending transactions (implementation depends on your schema)
    const hasPendingTransactions = false; // Add your logic here
    
    if (hasPendingTransactions && !args.force) {
      throw new Error("Cannot deactivate account with pending transactions");
    }

    await ctx.db.patch(args.accountId, {
      isActive: false,
      updatedAt: Date.now(),
    });

    return { 
      success: true, 
      message: "Account deactivated successfully",
      deactivatedAt: Date.now()
    };
  },
});
